﻿-- <PERSON><PERSON><PERSON> dữ liệu các bảng theo thứ tự tránh lỗi khóa ngoại
DELETE FROM "Product_Vendor";
DELETE FROM "Product";
DELETE FROM "Customer";
DELETE FROM "Vendor";
DELETE FROM "Material";
DELETE FROM "SpecialProductTaxRate";
DELETE FROM "ProcessingType";
DELETE FROM "Category";
DELETE FROM "Unit";

-- Reset lại sequence cho các bảng (ID chạy từ 1 trở lại)
ALTER SEQUENCE "Vendor_ID_seq" RESTART WITH 1;
ALTER SEQUENCE "Unit_ID_seq" RESTART WITH 1;
ALTER SEQUENCE "Category_ID_seq" RESTART WITH 1;
ALTER SEQUENCE "ProcessingType_ID_seq" RESTART WITH 1;
ALTER SEQUENCE "Customer_ID_seq" RESTART WITH 1;
ALTER SEQUENCE "SpecialProductTaxRate_ID_seq" RESTART WITH 1;
ALTER SEQUENCE "Material_ID_seq" RESTART WITH 1;
ALTER SEQUENCE "Product_ID_seq" RESTART WITH 1;

-- INSERT dữ liệu mẫu

-- Đơn vị tính (20 dòng)
INSERT INTO "Unit" ("Code", "Name", "Status", "CreatedBy", "UpdatedBy") VALUES
('UNIT01', 'Kilôgam', 'Actived', 1, 1),
('UNIT02', 'Lít', 'Actived', 1, 1),
('UNIT03', 'Thùng', 'Actived', 1, 1),
('UNIT04', 'Hộp', 'Actived', 1, 1),
('UNIT05', 'Gói', 'Actived', 1, 1),
('UNIT06', 'Chai', 'Actived', 1, 1),
('UNIT07', 'Cái', 'Actived', 1, 1),
('UNIT08', 'Bao', 'Actived', 1, 1),
('UNIT09', 'Lon', 'Actived', 1, 1),
('UNIT10', 'Cân', 'Actived', 1, 1),
('UNIT11', 'Túi', 'Actived', 1, 1),
('UNIT12', 'Viên', 'Actived', 1, 1),
('UNIT13', 'Thùng carton', 'Actived', 1, 1),
('UNIT14', 'Kg', 'Actived', 1, 1),
('UNIT15', 'm3', 'Actived', 1, 1),
('UNIT16', 'Cây', 'Actived', 1, 1),
('UNIT17', 'Gói nhỏ', 'Actived', 1, 1),
('UNIT18', 'Hộp lớn', 'Actived', 1, 1),
('UNIT19', 'Lạng', 'Actived', 1, 1),
('UNIT20', 'Thùng nhỏ', 'Actived', 1, 1);

-- Danh mục sản phẩm (20 dòng)
INSERT INTO "Category" ("Code", "Name", "Status", "CreatedBy", "UpdatedBy") VALUES
('CAT01', 'Thực phẩm tươi sống', 'Actived', 1, 1),
('CAT02', 'Thực phẩm chế biến', 'Actived', 1, 1),
('CAT03', 'Đồ uống', 'Actived', 1, 1),
('CAT04', 'Gia vị', 'Actived', 1, 1),
('CAT05', 'Đồ ăn nhanh', 'Actived', 1, 1),
('CAT06', 'Sản phẩm làm bánh', 'Actived', 1, 1),
('CAT07', 'Rau củ quả', 'Actived', 1, 1),
('CAT08', 'Thực phẩm đông lạnh', 'Actived', 1, 1),
('CAT09', 'Hải sản', 'Actived', 1, 1),
('CAT10', 'Sữa và sản phẩm từ sữa', 'Actived', 1, 1),
('CAT11', 'Thịt', 'Actived', 1, 1),
('CAT12', 'Ngũ cốc', 'Actived', 1, 1),
('CAT13', 'Bánh kẹo', 'Actived', 1, 1),
('CAT14', 'Đồ uống có cồn', 'Actived', 1, 1),
('CAT15', 'Nước giải khát', 'Actived', 1, 1),
('CAT16', 'Đồ hộp', 'Actived', 1, 1),
('CAT17', 'Thực phẩm hữu cơ', 'Actived', 1, 1),
('CAT18', 'Đồ ăn nhẹ', 'Actived', 1, 1),
('CAT19', 'Nguyên liệu chế biến', 'Actived', 1, 1),
('CAT20', 'Đồ uống đóng chai', 'Actived', 1, 1);

-- Kiểu chế biến (20 dòng)
INSERT INTO "ProcessingType" ("Code", "Name", "Status", "CreatedBy", "UpdatedBy") VALUES
('PROC01', 'Đông lạnh', 'Actived', 1, 1),
('PROC02', 'Đóng hộp', 'Actived', 1, 1),
('PROC03', 'Sấy khô', 'Actived', 1, 1),
('PROC04', 'Tươi sống', 'Actived', 1, 1),
('PROC05', 'Hấp', 'Actived', 1, 1),
('PROC06', 'Nướng', 'Actived', 1, 1),
('PROC07', 'Luộc', 'Actived', 1, 1),
('PROC08', 'Xông khói', 'Actived', 1, 1),
('PROC09', 'Rán', 'Actived', 1, 1),
('PROC10', 'Kho', 'Actived', 1, 1),
('PROC11', 'Làm lạnh', 'Actived', 1, 1),
('PROC12', 'Đóng gói', 'Actived', 1, 1),
('PROC13', 'Làm mềm', 'Actived', 1, 1),
('PROC14', 'Ủ men', 'Actived', 1, 1),
('PROC15', 'Cắt nhỏ', 'Actived', 1, 1),
('PROC16', 'Trộn', 'Actived', 1, 1),
('PROC17', 'Ép', 'Actived', 1, 1),
('PROC18', 'Chưng cất', 'Actived', 1, 1),
('PROC19', 'Làm đông', 'Actived', 1, 1),
('PROC20', 'Ủ lạnh', 'Actived', 1, 1);

-- Thuế sản phẩm đặc biệt (20 dòng)
INSERT INTO "SpecialProductTaxRate" ("Code", "Name", "Status", "CreatedBy", "UpdatedBy") VALUES
('TAX01', 'Thuế thực phẩm nhập khẩu', 'Actived', 1, 1),
('TAX02', 'Thuế vệ sinh an toàn', 'Actived', 1, 1),
('TAX03', 'Thuế hàng xa xỉ', 'Actived', 1, 1),
('TAX04', 'Thuế bảo vệ sức khỏe', 'Actived', 1, 1),
('TAX05', 'Thuế rượu bia', 'Actived', 1, 1),
('TAX06', 'Thuế thuốc lá', 'Actived', 1, 1),
('TAX07', 'Thuế môi trường', 'Actived', 1, 1),
('TAX08', 'Thuế dầu mỏ', 'Actived', 1, 1),
('TAX09', 'Thuế hàng điện tử', 'Actived', 1, 1),
('TAX10', 'Thuế nhập khẩu đặc biệt', 'Actived', 1, 1),
('TAX11', 'Thuế bán hàng', 'Actived', 1, 1),
('TAX12', 'Thuế tiêu thụ đặc biệt', 'Actived', 1, 1),
('TAX13', 'Thuế nhập khẩu ưu đãi', 'Actived', 1, 1),
('TAX14', 'Thuế chống bán phá giá', 'Actived', 1, 1),
('TAX15', 'Thuế chống trợ cấp', 'Actived', 1, 1),
('TAX16', 'Thuế môi trường phát thải', 'Actived', 1, 1),
('TAX17', 'Thuế khai thác tài nguyên', 'Actived', 1, 1),
('TAX18', 'Thuế đóng góp xã hội', 'Actived', 1, 1),
('TAX19', 'Thuế phí hải quan', 'Actived', 1, 1),
('TAX20', 'Thuế bảo hiểm', 'Actived', 1, 1);

-- Chất liệu (20 dòng)
INSERT INTO "Material" ("Code", "Name", "Status", "CreatedBy", "UpdatedBy") VALUES
('MAT01', 'Thịt heo', 'Actived', 1, 1),
('MAT02', 'Cá hồi', 'Actived', 1, 1),
('MAT03', 'Gạo', 'Actived', 1, 1),
('MAT04', 'Muối', 'Actived', 1, 1),
('MAT05', 'Đường', 'Actived', 1, 1),
('MAT06', 'Bơ', 'Actived', 1, 1),
('MAT07', 'Sữa', 'Actived', 1, 1),
('MAT08', 'Bột mì', 'Actived', 1, 1),
('MAT09', 'Dầu ăn', 'Actived', 1, 1),
('MAT10', 'Tiêu', 'Actived', 1, 1),
('MAT11', 'Ớt', 'Actived', 1, 1),
('MAT12', 'Hành', 'Actived', 1, 1),
('MAT13', 'Tỏi', 'Actived', 1, 1),
('MAT14', 'Cà rốt', 'Actived', 1, 1),
('MAT15', 'Khoai tây', 'Actived', 1, 1),
('MAT16', 'Bắp cải', 'Actived', 1, 1),
('MAT17', 'Cải thìa', 'Actived', 1, 1),
('MAT18', 'Dưa chuột', 'Actived', 1, 1),
('MAT19', 'Táo', 'Actived', 1, 1),
('MAT20', 'Chuối', 'Actived', 1, 1);

-- Nhà cung cấp (20 dòng)
INSERT INTO "Vendor" ("Code", "Name", "Status", "CreatedBy", "UpdatedBy") VALUES
('VEND01', 'Công ty Thực phẩm ABC', 'Actived', 1, 1),
('VEND02', 'Siêu thị XYZ', 'Actived', 1, 1),
('VEND03', 'Cửa hàng Tiện lợi 123', 'Actived', 1, 1),
('VEND04', 'Công ty Nông sản Miền Tây', 'Actived', 1, 1),
('VEND05', 'Công ty Xuất nhập khẩu ABC', 'Actived', 1, 1),
('VEND06', 'Nhà phân phối XYZ', 'Actived', 1, 1),
('VEND07', 'Cửa hàng Rau sạch', 'Actived', 1, 1),
('VEND08', 'Công ty Thực phẩm X', 'Actived', 1, 1),
('VEND09', 'Siêu thị An Bình', 'Actived', 1, 1),
('VEND10', 'Cửa hàng Tiện lợi 456', 'Actived', 1, 1),
('VEND11', 'Nhà cung cấp thực phẩm Y', 'Actived', 1, 1),
('VEND12', 'Công ty thực phẩm Z', 'Actived', 1, 1),
('VEND13', 'Đại lý phân phối A', 'Actived', 1, 1),
('VEND14', 'Nhà cung cấp B', 'Actived', 1, 1),
('VEND15', 'Công ty Nông sản Đông Nam', 'Actived', 1, 1),
('VEND16', 'Siêu thị Hòa Bình', 'Actived', 1, 1),
('VEND17', 'Cửa hàng thực phẩm C', 'Actived', 1, 1),
('VEND18', 'Nhà phân phối D', 'Actived', 1, 1),
('VEND19', 'Công ty Thực phẩm E', 'Actived', 1, 1),
('VEND20', 'Cửa hàng G', 'Actived', 1, 1);

-- Khách hàng (20 dòng)
INSERT INTO "Customer" ("Code", "Name", "Status", "CreatedBy", "UpdatedBy") VALUES
('CUS01', 'Nguyễn Văn A', 'Actived', 1, 1),
('CUS02', 'Trần Thị B', 'Actived', 1, 1),
('CUS03', 'Công ty TNHH Thực phẩm An Toàn', 'Actived', 1, 1),
('CUS04', 'Lê Văn C', 'Actived', 1, 1),
('CUS05', 'Phạm Thị D', 'Actived', 1, 1),
('CUS06', 'Công ty XYZ', 'Actived', 1, 1),
('CUS07', 'Nguyễn Thị E', 'Actived', 1, 1),
('CUS08', 'Trần Văn F', 'Actived', 1, 1),
('CUS09', 'Công ty GHI', 'Actived', 1, 1),
('CUS10', 'Lê Thị H', 'Actived', 1, 1),
('CUS11', 'Nguyễn Văn I', 'Actived', 1, 1),
('CUS12', 'Trần Thị J', 'Actived', 1, 1),
('CUS13', 'Công ty KLM', 'Actived', 1, 1),
('CUS14', 'Lê Văn M', 'Actived', 1, 1),
('CUS15', 'Phạm Thị N', 'Actived', 1, 1),
('CUS16', 'Công ty NOP', 'Actived', 1, 1),
('CUS17', 'Nguyễn Thị O', 'Actived', 1, 1),
('CUS18', 'Trần Văn P', 'Actived', 1, 1),
('CUS19', 'Công ty QRS', 'Actived', 1, 1),
('CUS20', 'Lê Thị T', 'Actived', 1, 1);

-- Sản phẩm (20 dòng)
INSERT INTO "Product" (
    "Code", "Name", "Unit_ID", "Category_ID", "ProcessingType_ID",
    "TaxRate", "Material_ID", "CompanyTaxRate", "ConsumerTaxRate",
    "SpecialProductTaxRate_ID", "Status", "CreatedBy", "UpdatedBy"
) VALUES
('PROD01', 'Thịt heo đông lạnh', 1, 1, 1, 8.00, 1, 5.00, 7.00, 1, 'Actived', 1, 1),
('PROD02', 'Cá hồi đóng hộp', 2, 2, 2, 12.00, 2, 6.00, 9.00, 2, 'Actived', 1, 1),
('PROD03', 'Gạo sấy khô', 1, 1, 3, 5.00, 3, 3.00, 4.00, 3, 'Actived', 1, 1),
('PROD04', 'Muối tươi', 4, 4, 4, 3.00, 4, 2.00, 2.50, 4, 'Actived', 1, 1),
('PROD05', 'Đường kính', 5, 1, 5, 4.00, 5, 3.00, 3.50, 5, 'Actived', 1, 1),
('PROD06', 'Bơ nhạt', 6, 6, 6, 6.00, 6, 4.00, 5.00, 6, 'Actived', 1, 1),
('PROD07', 'Sữa tươi', 7, 10, 7, 10.00, 7, 6.00, 8.00, 7, 'Actived', 1, 1),
('PROD08', 'Bột mì đa dụng', 8, 6, 8, 7.00, 8, 5.00, 6.00, 8, 'Actived', 1, 1),
('PROD09', 'Dầu ăn thực vật', 9, 1, 9, 9.00, 9, 7.00, 8.50, 9, 'Actived', 1, 1),
('PROD10', 'Tiêu đen', 10, 4, 10, 3.00, 10, 2.50, 3.50, 10, 'Actived', 1, 1),
('PROD11', 'Ớt bột', 11, 4, 11, 4.00, 11, 3.50, 4.00, 11, 'Actived', 1, 1),
('PROD12', 'Hành tây', 12, 7, 12, 2.00, 12, 2.00, 2.50, 12, 'Actived', 1, 1),
('PROD13', 'Tỏi khô', 13, 4, 13, 3.00, 13, 2.50, 3.00, 13, 'Actived', 1, 1),
('PROD14', 'Cà rốt tươi', 14, 7, 14, 2.50, 14, 2.00, 2.50, 14, 'Actived', 1, 1),
('PROD15', 'Khoai tây', 15, 7, 15, 3.00, 15, 2.50, 3.00, 15, 'Actived', 1, 1),
('PROD16', 'Bắp cải', 16, 7, 16, 2.00, 16, 1.50, 2.00, 16, 'Actived', 1, 1),
('PROD17', 'Cải thìa', 17, 7, 17, 2.00, 17, 1.50, 2.00, 17, 'Actived', 1, 1),
('PROD18', 'Dưa chuột', 18, 7, 18, 2.00, 18, 1.50, 2.00, 18, 'Actived', 1, 1),
('PROD19', 'Táo đỏ', 19, 19, 19, 4.00, 19, 3.00, 3.50, 19, 'Actived', 1, 1),
('PROD20', 'Chuối tiêu', 20, 20, 20, 3.00, 20, 2.50, 3.00, 20, 'Actived', 1, 1);

-- Bảng liên kết Sản phẩm - Nhà cung cấp (20 dòng)
INSERT INTO "Product_Vendor" ("Vendor_ID", "Product_ID", "Price", "UnitPrice", "Priority") VALUES
(1, 1, 150000, 150000, 1),
(2, 2, 120000, 120000, 1),
(3, 3, 100000, 100000, 1),
(4, 4, 90000, 90000, 1),
(5, 5, 110000, 110000, 1),
(6, 6, 130000, 130000, 1),
(7, 7, 140000, 140000, 1),
(8, 8, 160000, 160000, 1),
(9, 9, 170000, 170000, 1),
(10, 10, 90000, 90000, 1),
(11, 11, 85000, 85000, 1),
(12, 12, 95000, 95000, 1),
(13, 13, 105000, 105000, 1),
(14, 14, 97000, 97000, 1),
(15, 15, 88000, 88000, 1),
(16, 16, 93000, 93000, 1),
(17, 17, 102000, 102000, 1),
(18, 18, 94000, 94000, 1),
(19, 19, 99000, 99000, 1),
(20, 20, 101000, 101000, 1);
